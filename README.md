# AWP Infrastructure Service

Инфраструктурный сервис AWP. TODO

## Основные проекты

- **Host** - веб-приложение ASP.NET Core, точка входа в систему
  - Контроллеры Web API
  - Конфигурация сервисов и middleware
  - Swagger/OpenAPI документация

- **Domain** - доменная модель и бизнес-логика
  - Доменные сущности
  - Бизнес-правила и валидация
  - Интерфейсы для внешних зависимостей

- **DAL (Data Access Layer)** - слой доступа к данным
  - Entity Framework Core контекст
  - Репозитории и конфигурация сущностей
  - Миграции базы данных

