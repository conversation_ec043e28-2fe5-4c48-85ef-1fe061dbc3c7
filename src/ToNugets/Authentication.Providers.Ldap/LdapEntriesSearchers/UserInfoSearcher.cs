using System.Diagnostics;
using System.DirectoryServices.Protocols;
using System.Text;
using Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;
using Platform.AWP.Authentication.Providers.Ldap.LdapEntries;
using Platform.Identities.Base;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapEntriesSearchers;

internal class UserInfoSearcher : ProviderBase
{
    private readonly DomainsProcessor _domainsProcessor;

    public UserInfoSearcher(TimeSpan cacheExpirationTimeout, DomainsProcessor domainsProcessor) : base(
        cacheExpirationTimeout)
    {
        _domainsProcessor = domainsProcessor;
    }

    public async Task<UserIdentityInfo> FindUserInfo(Guid userId)
    {
        return await GetFromCacheOrSet($"{nameof(FindUserInfo)}({userId})",
            async () => await DoFindUserInfo(userId));
    }

    private async Task<UserIdentityInfo> DoFindUserInfo(Guid userId)
    {
        SearchResultEntry userEntry = null;
        string userDomain = null;
        foreach (var fullDomainName in _domainsProcessor.FullDomainNames)
        {
            var ldapConnectionWrapper = _domainsProcessor.GetLdapConnection(fullDomainName);

            var searchRequest = BuildSearchRequestForUserById(userId, fullDomainName,
                LdapAttributeNames.UserPrincipalName, LdapAttributeNames.SamAccountName,
                LdapAttributeNames.ObjectSid, LdapAttributeNames.ObjectGuid);
            var searchResult = await ldapConnectionWrapper.ExecuteLdapAction(async ldapConnection => await ldapConnection.FindEntries(searchRequest));

            if (searchResult.Count != 1) continue;

            userEntry = searchResult[0];
            userDomain = fullDomainName;
            break;
        }

        if (userEntry == null) return null;

        var attrs = userEntry.Attributes;

        var sid = LdapAttributes.LdapAttributes.ObjectSid.GetValue(attrs);
        var upn = LdapAttributes.LdapAttributes.UserPrincipalName.GetValue(attrs);
        Trace.WriteLine("upn: " + upn);
        var samAccountName = LdapAttributes.LdapAttributes.SamAccountName.GetValue(attrs);
        var guid = LdapAttributes.LdapAttributes.ObjectGuid.GetValue(attrs);

        return new UserIdentityInfo(userDomain, userEntry.DistinguishedName, sid, samAccountName, guid, upn);
    }

    private SearchRequest BuildSearchRequestForUserById(Guid entityId, string domainName, params string[] attributes)
    {
        var domainDistinguishedName = _domainsProcessor.GetDomainDistinguishedName(domainName);

        var entityIdBytes = entityId.ToByteArray();
        var hex = new StringBuilder(entityIdBytes.Length * 3);
        foreach (var b in entityIdBytes)
        {
            hex.AppendFormat("\\{0:x2}", b);
        }

        var searchFilter = $"(&(objectClass=user)({LdapAttributeNames.ObjectGuid}={hex}))";

        return new SearchRequest(domainDistinguishedName, searchFilter, SearchScope.Subtree, attributes)
        { Aliases = DereferenceAlias.Always };
    }
}
