using System.DirectoryServices.Protocols;
using System.Text;

namespace Platform.AWP.Authentication.Providers.Ldap.LdapAttributes;

internal class LdapSidAttribute : LdapSimpleAttribute<string>
{
    public LdapSidAttribute(string attributeName)
        : base(attributeName)
    {
    }

    public override bool TryGetValue(SearchResultAttributeCollection attributeCollection, out string result)
    {
        if (!TryGetValue<byte[]>(attributeCollection, out var sidBytes))
        {
            result = null;
            return false;
        }

        result = DecodeSid(sidBytes);
        return true;
    }

    //	слизано отсюда: https://ldapwiki.com/wiki/ObjectSID
    private static string DecodeSid(byte[] sid)
    {
        var strSid = new StringBuilder("S-");

        // get byte(0) - revision level
        var revision = sid[0];
        strSid.Append($"{revision}");

        //next byte byte(1) - count of sub-authorities
        var countSubAuths = sid[1] & 0xFF;

        //byte(2-7) - 48 bit authority ([Big-Endian])
        long authority = 0;
        //String rid = "";
        for (var i = 2; i <= 7; i++)
        {
            authority |= (long)sid[i] << (8 * (5 - (i - 2)));
        }

        strSid.Append("-");
        //strSid.Append(Long.toHexString(authority));
        strSid.AppendFormat("{0:x}", authority);

        //iterate all the sub-auths and then countSubAuths x 32 bit sub authorities ([Little-Endian])
        var offset = 8;
        var size = 4; //4 bytes for each sub auth
        for (var j = 0; j < countSubAuths; j++)
        {
            long subAuthority = 0;
            for (var k = 0; k < size; k++)
            {
                subAuthority |= (long)(sid[offset + k] & 0xFF) << (8 * k);
            }

            // format it
            strSid.Append("-");
            strSid.Append(subAuthority);
            offset += size;
        }

        return strSid.ToString();
    }
}
