using System.DirectoryServices.Protocols;
using Microsoft.Extensions.Logging;
using Platform.Logging.MicrosoftExtensions;

namespace Platform.AWP.Authentication.Providers.Ldap;

internal static class LdapHelper
{
    public static async Task<SearchResultEntry> FindEntry(this LdapConnection connection, SearchRequest searchRequest)
    {
        var foundEntries = await connection.FindEntries(searchRequest);
        return foundEntries?.Count > 0 ? foundEntries[0] : null;
    }

    public static async Task<SearchResultEntry> FindEntry(this LdapConnection connection, SearchRequest searchRequest, ILogger logger)
    {
        using var mlh = typeof(LdapHelper).CreateMethodLogHelperNoImplicitLogging(logger).WithArgs("searchRequest={@searchRequest}", searchRequest);
        try
        {
            return await connection.FindEntry(searchRequest);
        }
        catch (LdapException ldapExc)
        {
            mlh.LogMethodFailed(ldapExc);
            mlh.LogMethodError(ldapExc.InnerException, "xxx: ErrorCode={errorCode}, ServerErrorMessage={serverErrorMessage}", ldapExc.ErrorCode, ldapExc.ServerErrorMessage);
            throw;
        }
    }

    public static async Task<SearchResultEntryCollection> FindEntries(this LdapConnection connection, SearchRequest searchRequest)
    {
        var response = (SearchResponse)await connection.SendRequestAsync(searchRequest);
        return response?.Entries;
    }

    public static async Task<DirectoryResponse> SendRequestAsync(this LdapConnection connection, SearchRequest request)
    {
        if (connection == null)
        {
            throw new ArgumentNullException(nameof(connection));
        }

        return await Task<DirectoryResponse>.Factory.FromAsync(connection.BeginSendRequest, connection.EndSendRequest, request, PartialResultProcessing.NoPartialResultSupport, null);
    }
}
