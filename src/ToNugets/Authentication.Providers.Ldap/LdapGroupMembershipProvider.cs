using System.Diagnostics;
using System.Security.Claims;
using Platform.AWP.Authentication.Providers.Ldap.LdapEntries;
using Platform.AWP.Authentication.Providers.Ldap.LdapEntriesSearchers;
using Platform.Identities.Base;

#pragma warning disable CS1591

namespace Platform.AWP.Authentication.Providers.Ldap;

public class LdapGroupMembershipProvider : GroupMembershipProviderBase
{
    private readonly GroupInfoSearcher _groupInfoSearcher;
    private readonly UserInfoSearcher _userInfoSearcher;

    public LdapGroupMembershipProvider(TimeSpan cacheExpirationTimeout, DomainsProcessor domainsProcessor)
        : base(cacheExpirationTimeout)
    {
        _userInfoSearcher = new UserInfoSearcher(cacheExpirationTimeout, domainsProcessor);
        _groupInfoSearcher = new GroupInfoSearcher(cacheExpirationTimeout, domainsProcessor);
    }

    protected override Task<bool> IsPrincipalInGroup(ClaimsPrincipal claimsPrincipal, string groupName)
    {
        return Task.FromResult(false);
    }

    protected override async Task<bool> IsUserInGroup(Guid userId, Guid groupId)
    {
        var sw = Stopwatch.StartNew();
        var prefix = $"TIMING FOR {nameof(IsUserInGroup)}({userId}, {groupId}): ";
        try
        {
            var result = await DoIsUserInGroup(userId, groupId);
            Trace.WriteLine($"{prefix}: {sw.ElapsedMilliseconds}, result='{result}'");
            return result;
        }
        catch (Exception exc)
        {
            Trace.TraceError($"{prefix}: {sw.ElapsedMilliseconds}, error={exc}");
            throw;
        }
    }

    private async Task<bool> DoIsUserInGroup(Guid userId, Guid groupId)
    {
        if (userId == groupId)
        {
            return true;
        }

        //	сначала ищем группу, а потом пользователя, потому что вероятность вызова IsInGroup(user, group) или IsInGroup(user1, user2) выше, чем вероятность вызова IsInGroup(group, user) или IsInGroup(group1, group2)
        var groupInfo = await _groupInfoSearcher.FindGroupInfo(groupId);
        if (groupInfo == null)
        {
            var userByGroupId = await _userInfoSearcher.FindUserInfo(groupId);
            if (userByGroupId != null)
            {
                //	это значит, что на вход пришло IsInGroup(user1, user2) или IsInGroup(group, user). Первый вариант - проверен в самом начале (сравнение идентификаторов), второй - по-любому false
                return false;
            }
        }

        if (groupInfo == null)
        {
            throw new ArgumentException("Could not find group with specified Id", nameof(groupId));
        }

        IdentityInfoBase userInfo = await _userInfoSearcher.FindUserInfo(userId);
        if (userInfo == null)
        {
            //	может быть, на вход пришло что-то типа IsInGroup(group1, group2)
            userInfo = await _groupInfoSearcher.FindGroupInfo(userId);
        }

        if (userInfo == null)
        {
            throw new ArgumentException("Could not find user with specified Id", nameof(userId));
        }


        return await IsUserInGroup(userInfo, groupInfo);
    }

    private async Task<bool> IsUserInGroup(IdentityInfoBase user, GroupIdentityInfoWithMembers group)
    {
        if (user == null || group == null)
        {
            return false;
        }

        if (IdentityIsInGroupMembers(user, group))
        {
            return true;
        }

        //	поищем по под-группам (которые в том же домене)
        foreach (var subGroup in group.Members.Where(x => x.EntryType == LdapEntryType.Group))
        {
            var subGroupInfo = subGroup as GroupIdentityInfo;
            if (subGroupInfo == null)
            {
                throw new Exception("Suddenly subGroupInfo == null");
            }

            var subGroupInfoWithMembers = await _groupInfoSearcher.FindGroupInfo(subGroupInfo.ObjectGuid);

            if (await IsUserInGroup(user, subGroupInfoWithMembers))
            {
                return true;
            }
        }
        //	TODO: ANB: а дальше - среди FSP могут быть также группы, по ним тоже надо искать...

        return false;
    }

    private static bool IdentityIsInGroupMembers(IdentityInfoBase user, GroupIdentityInfoWithMembers group)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        if (group == null) throw new ArgumentNullException(nameof(group));

        if (group.Members == null) throw new ArgumentException("Group members is null!", nameof(group));

        var userOrGroupByDn = group.Members.FirstOrDefault(x => x.EntryType == user.EntryType && string.Equals(x.DistinguishedName, user.DistinguishedName));
        if (userOrGroupByDn != null)
        {
            return true;
        }

        //	TODO: ANB: надо переделать поиск по sid (просто Contains - как-то тупо... хотя работать будет, скорее всего, без проблем)
        var foundFspBySid = group.Members.FirstOrDefault(x => x.EntryType == LdapEntryType.ForeignSecurityPrincipal && x.DistinguishedName.Contains(user.Sid));
        if (foundFspBySid != null)
        {
            return true;
        }

        return false;
    }
}
