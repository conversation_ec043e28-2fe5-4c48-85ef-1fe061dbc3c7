using Microsoft.AspNetCore.Mvc;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.Domain.Entities;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers.Operators;

public class ActualOperatorStatusesController : InfrastructureApiControllerBase, IActualOperatorStatusesWebApi
{
    public ActualOperatorStatusesController(IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
    }

    [HttpPost]
    public async Task<OperatorStatusInfo[]> GetActualOperatorsStatuses([FromBody] ActualOperatorStatusFilter actualOperatorStatusFilter)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("actualOperatorStatusFilter={@actualOperatorStatusFilter}", actualOperatorStatusFilter);

        // return await mlh.ExecuteWithTryAsync(async () =>
        // {
        //     using var da = new ActualOperatorStatusDataAccessor(LoggerFactory);
        //     return await da.GetActualOperatorsStatuses(actualOperatorStatusFilter);
        // });
        return Array.Empty<OperatorStatusInfo>();
    }
}
