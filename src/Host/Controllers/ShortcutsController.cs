using Microsoft.AspNetCore.Mvc;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Platform.OperationInfo;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;
using Product.AWP.Infrastructure.Domain.Entities;
using Product.AWP.Infrastructure.Domain.Interfaces;

namespace Product.AWP.Infrastructure.Controllers;

public class ShortcutsController : InfrastructureApiControllerBase, IShortcutsWebApi
{
    private const string CustomShortcutsConfigurationVersionKey = "CustomShortcuts";

    public ShortcutsController(IOperationInfoProvider operationInfoProvider, IAuditor auditor, ILoggerFactory loggerFactory, IIdentityProvider identityProvider, IGroupMembershipProvider groupMembershipProvider)
        : base(operationInfoProvider, auditor, loggerFactory, identityProvider, groupMembershipProvider)
    {
    }

    /// <summary>
    /// Gets list of shortcuts for current user
    /// </summary>
    [HttpGet]
    public async Task<Shortcut[]> GetShortcutsForCurrentUser(Guid userRoleId, Guid? serviceAreaId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userRoleId={userRoleId}, serviceAreaId={serviceAreaId}", userRoleId, serviceAreaId);

        // return await mlh.ExecuteWithTryAsync(async () =>
        // {
        //     using var da = new ShortcutsDataAccessor(LoggerFactory);
        //     return await da.GetShortcutsForCurrentUser(serviceAreaId, userRoleId);
        // });
        return null;
    }

    /// <summary>
    /// Gets list of shortcuts for current user
    /// </summary>
    [HttpGet]
    public async Task<UserShortcut[]> GetCurrentUserCustomShortcuts()
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithNoArgs();

        // return await mlh.ExecuteWithTryAsync(async () =>
        // {
        //     var userLogin = (OperationInfoProvider.GetCurrentOperationInfo() ?? OperationInfoProvider.GetPreviousOperationsInfo()?.FirstOrDefault()).Login;
        //     using var da = new ShortcutsDataAccessor(LoggerFactory);
        //     return await da.GetCurrentUserCustomShortcuts(userLogin);
        // });
        return null;
    }

    /// <summary>
    /// Adds shortcut for current user
    /// </summary>
    /// <param name="userShortcut">
    /// The shortcut.
    /// </param>
    [HttpPut]
    public async Task AddCurrentUserCustomShortcut([FromBody] UserShortcut userShortcut)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcut={@userShortcut}", userShortcut);

        // await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
        //     mlh,
        //     () => new ShortcutsDataAccessor(LoggerFactory),
        //     async da =>
        //     {
        //         var userLogin = (OperationInfoProvider.GetCurrentOperationInfo() ?? OperationInfoProvider.GetPreviousOperationsInfo()?.FirstOrDefault()).Login;
        //         using var cvda = new ConfigurationVersionDataAccessor(LoggerFactory);

        //         await da.AddCurrentUserCustomShortcut(userShortcut, userLogin);
        //         await cvda.UpdateVersion(CustomShortcutsConfigurationVersionKey);
        //     });
    }

    /// <summary>
    /// Updates current user shortcut
    /// </summary>
    /// <param name="userShortcut">
    /// The user shortcut.
    /// </param>
    [HttpPost]
    public async Task UpdateCurrentUserCustomShortcut([FromBody] UserShortcut userShortcut)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcut={@userShortcut}", userShortcut);

        // await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
        //     mlh,
        //     () => new ShortcutsDataAccessor(LoggerFactory),
        //     async da =>
        //     {
        //         using var cvda = new ConfigurationVersionDataAccessor(LoggerFactory);

        //         await da.UpdateCurrentUserCustomShortcut(userShortcut);
        //         await cvda.UpdateVersion(CustomShortcutsConfigurationVersionKey);
        //     });
    }

    /// <summary>
    /// Removes current user shortcut
    /// </summary>
    /// <param name="userShortcutId">
    /// Id of user shortcut.
    /// </param>
    [HttpDelete]
    public async Task RemoveCurrentUserCustomShortcut([FromQuery] Guid userShortcutId)
    {
        using var mlh = this.CreateMethodLogHelper(Logger).WithArgs("userShortcutId={userShortcutId}", userShortcutId);

        // await CreateAuditExecutionHelper().ExecuteWithTryWithAudit(
        //     mlh,
        //     () => new ShortcutsDataAccessor(LoggerFactory),
        //     async da =>
        //     {
        //         using var cvda = new ConfigurationVersionDataAccessor(LoggerFactory);

        //         await da.RemoveCurrentUserCustomShortcut(userShortcutId);
        //         await cvda.UpdateVersion(CustomShortcutsConfigurationVersionKey);
        //     });
    }
}
