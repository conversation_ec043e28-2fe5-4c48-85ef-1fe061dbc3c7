using Product.AWP.Infrastructure.Configurations.OperatorAuthentication;
using Product.AWP.Infrastructure.DEL;
using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.Logic;

internal class OperatorCustomAttributesManager
{
    private readonly ILogger _logger;

    public OperatorCustomAttributesManager(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger(GetType());
    }

    internal async Task AddOrUpdateCustomAttributesFromActiveDirectory(Operator currentOperator, AwpUser activeDirectoryUser, CustomAttributeFillSetting[] fillSettings, OperatorDataAccessor operatorDataAccessor)
    {
        // TODO: Поправить

        // if (currentOperator == null)
        // {
        // 	throw new ArgumentNullException(nameof(currentOperator));
        // }
        //
        // if (activeDirectoryUser?.CustomAttributes == null || activeDirectoryUser.CustomAttributes.Count == 0 || fillSettings == null)
        // {
        // 	return;
        // }
        //
        // var existAttributes = await operatorDataAccessor.GetCustomAttributes(new List<Guid> { currentOperator.Id }, activeDirectoryUser.CustomAttributes.Select(p => p.Item1).ToList());
        // var attributesToUpdate = new List<OperatorCustomAttribute>();
        //
        // using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("currentOperator.UserName={currentOperator_UserName}, activeDirectoryUser.UserName={activeDirectoryUser_UserName}", currentOperator.UserName, activeDirectoryUser.UserName);
        // foreach (var setting in activeDirectoryUser.CustomAttributes)
        // {
        // 	var fillSetting = fillSettings.FirstOrDefault(p => p.CustomAttrCode == setting.Item1);
        // 	if (fillSetting == null)
        // 	{
        // 		mlh.LogMethodDetails($"Not found mapping for custom attribute with code={setting.Item1}");
        // 		continue;
        // 	}
        //
        // 	var newAttribute = GetOperatorCustomAttribute(currentOperator.Id, setting.Item2, fillSetting);
        //
        // 	//Если у оператора нет такого атрибута, то сразу добавляем
        // 	if (existAttributes.All(x => x.Code != fillSetting.CustomAttrCode))
        // 	{
        // 		attributesToUpdate.Add(newAttribute);
        // 		mlh.LogMethodDetails($"Add new custom attribute with code={fillSetting.CustomAttrCode} from Active Directory");
        // 		continue;
        // 	}
        //
        // 	//Если есть то сравниваем значения
        // 	var existAttribute = existAttributes.First(x => x.Code == fillSetting.CustomAttrCode);
        // 	if (IsEqualAttributeValues(existAttribute, newAttribute, fillSetting.CustomAttrType))
        // 	{
        // 		continue;
        // 	}
        //
        // 	mlh.LogMethodDetails($"Update custom attribute with code={fillSetting.CustomAttrCode} from Active Directory");
        // 	attributesToUpdate.Add(newAttribute);
        // }
        //
        // await operatorDataAccessor.AddOrUpdateCustomAttributes(attributesToUpdate);
        //
        // if (attributesToUpdate.Any())
        // {
        // 	mlh.LogMethodDetails($"Custom attributes updated. Count={attributesToUpdate.Count}", this, null);
        // }
    }

    private static bool IsEqualAttributeValues(OperatorCustomAttribute currentAttr, OperatorCustomAttribute newAttribute, CustomAttributeType attrType)
    {
        switch (attrType)
        {
            case CustomAttributeType.String:
                return currentAttr.StringValue == newAttribute.StringValue;
            case CustomAttributeType.Bool:
                return currentAttr.BoolValue == newAttribute.BoolValue;
            case CustomAttributeType.Guid:
                return currentAttr.GuidValue == newAttribute.GuidValue;
            case CustomAttributeType.Long:
                return currentAttr.LongValue == newAttribute.LongValue;
            case CustomAttributeType.Decimal:
                return currentAttr.DecimalValue == newAttribute.DecimalValue;
            case CustomAttributeType.DateTime:
                return currentAttr.DateTimeValue == newAttribute.DateTimeValue;
            case CustomAttributeType.Binary:
                return currentAttr.BinaryValue == newAttribute.BinaryValue;
        }

        throw new Exception("Wrong custom attribute type!");
    }

    private static OperatorCustomAttribute GetOperatorCustomAttribute(Guid operatorId, object value, CustomAttributeFillSetting setting)
    {
        var customAttr = new OperatorCustomAttribute
        {
            OperatorId = operatorId,
            Code = setting.CustomAttrCode
        };

        switch (setting.CustomAttrType)
        {
            case CustomAttributeType.String:
                customAttr.StringValue = (string)value;
                break;
            case CustomAttributeType.Bool:
                customAttr.BoolValue = (bool)value;
                break;
            case CustomAttributeType.Guid:
                customAttr.GuidValue = (Guid)value;
                break;
            case CustomAttributeType.Long:
                customAttr.LongValue = value as long?;
                break;
            case CustomAttributeType.Decimal:
                customAttr.DecimalValue = (decimal)value;
                break;
            case CustomAttributeType.DateTime:
                customAttr.DateTimeValue = (DateTime)value;
                break;
            case CustomAttributeType.Binary:
                customAttr.BinaryValue = value as byte[];
                break;
        }

        return customAttr;
    }
}
