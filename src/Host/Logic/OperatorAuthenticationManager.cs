using System.Security.Claims;
using System.Security.Principal;
using Platform.Identities;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.Configurations.OperatorAuthentication;
using Product.AWP.Infrastructure.DEL;
using Product.AWP.Infrastructure.Domain.Entities;
using Identity = Platform.Identities.Identity;

namespace Product.AWP.Infrastructure.Logic;
// TODO: Поправить

internal class OperatorAuthenticationManager
{
    private readonly OperatorAuthenticationConfiguration _configuration;
    private readonly IIdentityProvider _identityProvider;
    private readonly ILogger _logger;
    private readonly OperatorCustomAttributesManager _operatorCustomAttributesManager;

    public OperatorAuthenticationManager(ILoggerFactory loggerFactory, IIdentityProvider identityProvider, OperatorAuthenticationConfiguration configuration)
    {
        _logger = loggerFactory.CreateLogger(GetType());
        _identityProvider = identityProvider ?? throw new ArgumentNullException(nameof(identityProvider));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _operatorCustomAttributesManager = new OperatorCustomAttributesManager(loggerFactory);
    }

    public async Task<Operator> GetAuthenticatedOperator(IIdentity authenticatedIdentity, OperatorDataAccessor operatorDataAccessor, OperatorGroupDataAccessor operatorGroupDataAccessor)
    {
        // using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("authenticatedIdentity={authenticatedIdentity}", authenticatedIdentity);
        //
        // var awpUser = await TryFindUserInIdentityProvider(authenticatedIdentity, _configuration);
        // var currentOperator = await GetKnownOperator(authenticatedIdentity, _configuration, operatorDataAccessor, awpUser);
        //
        // if (currentOperator != null)
        // {
        // 	mlh.LogMethodDetails("Authenticate: operator authenticated - returning authenticated operator.");
        // }
        // else
        // {
        // 	currentOperator = await AddNewOperator(authenticatedIdentity, _configuration.NewOperatorDefaultGroupNames, operatorDataAccessor, operatorGroupDataAccessor, awpUser);
        // }
        //
        // try
        // {
        // 	await _operatorCustomAttributesManager.AddOrUpdateCustomAttributesFromActiveDirectory(currentOperator, awpUser, _configuration.CustomAttributeFillSettings, operatorDataAccessor);
        // 	currentOperator.CustomAttributes = await operatorDataAccessor.GetCustomAttributes(new List<Guid> {currentOperator.Id}, null);
        // }
        // catch (Exception e)
        // {
        //              mlh.LogMethodError(e, "AddOrUpdateCustomAttributesFromActiveDirectory FAILED!");
        // }
        //
        // return BuildOperator(currentOperator, authenticatedIdentity, awpUser, _configuration.DataSource);
        return null;
    }

    private string TryGetClaimValue(ClaimsIdentity claimsIdentity, string claimType)
    {
        if (claimsIdentity == null) return null;
        if (string.IsNullOrWhiteSpace(claimType)) return null;
        return claimsIdentity.FindFirst(claimType)?.Value;
    }

    private async Task<Operator> AddNewOperator(IIdentity identity, string[] newOperatorDefaultGroupNames, OperatorDataAccessor operatorDataAccessor, OperatorGroupDataAccessor operatorGroupDataAccessor, AwpUser awpUser)
    {
        //          using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("identity={identity}", identity);
        //
        //          var authenticatedOperator = new Operator
        // {
        // 	Id = Guid.NewGuid(),
        // 	UserName = awpUser?.UserName ?? identity.Name,
        // 	FirstName = awpUser?.FirstName,
        // 	MiddleName = awpUser?.MiddleName,
        // 	LastName = awpUser?.LastName,
        // 	ActiveDirectoryId = awpUser?.ActiveDirectoryId ?? Guid.Empty,
        // };
        //
        // try
        // {
        // 	mlh.LogMethodDetails("Authenticate: add new operator");
        // 	await operatorDataAccessor.Add(authenticatedOperator);
        //              mlh.LogMethodDetails("Authenticate: add new operator COMPLETED.");
        //
        // 	if (newOperatorDefaultGroupNames?.Length > 0)
        // 	{
        // 		var newOperatorDefaultGroups = await operatorGroupDataAccessor.GetOperatorGroupIds(newOperatorDefaultGroupNames);
        // 		await operatorGroupDataAccessor.AddOperatorToGroups(authenticatedOperator.Id, newOperatorDefaultGroups);
        // 	}
        //
        //              mlh.LogMethodDetails("Authenticate: operator added - returning added operator.");
        // }
        // catch (Exception exc)
        // {
        //              mlh.LogMethodError(exc, "Authenticate: add operator FAILED!!!");
        //              mlh.LogMethodDetails("Authenticate: operator NOT added - throwing error");
        // 	throw;
        // }
        //
        // return authenticatedOperator;
        return null;
    }

    private async Task<Operator> GetKnownOperator(IIdentity identity, OperatorAuthenticationConfiguration configuration, OperatorDataAccessor operatorDataAccessor, AwpUser awpUser)
    {
        return null;
        // var userName = identity.Name;
        // var authenticationMode = configuration.AuthenticationMode;
        // using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("userName={userName}, authenticationMode={authenticationMode}", userName, authenticationMode);
        //
        // Operator knownOperator;
        // try
        // {
        //              mlh.LogMethodDetails("Authenticate: find authenticated operator");
        // 	switch (configuration.AuthenticationMode)
        // 	{
        // 		case AuthenticationMode.Username:
        // 			//Если пользователя нет в каталоге - просто ищем текущего из identity
        // 			var identityLogin = identity.Name;
        //
        // 			knownOperator = await operatorDataAccessor.FindOperatorByIdentity(identity, _identityProvider);
        //
        // 			if (knownOperator != null || awpUser == null)
        // 			{
        // 				break;
        // 			}
        //
        //                      mlh.LogMethodDetails("GetKnownOperator: FindByUserNameContains returned null for '{identityLogin}', trying to find by ActiveDirectoryId='{awpUser_ActiveDirectoryId}'", identityLogin, awpUser.ActiveDirectoryId);
        // 			//	может быть такое, что в БД логин в одном формате (через @), а тут - вдругом (через \). Если известна инфа из АД - попробуем поискать по ней ещё
        // 			var dbUserByAdId = await operatorDataAccessor.FindByActiveDirectoryId(awpUser.ActiveDirectoryId);
        // 			if (dbUserByAdId == null)
        // 			{
        //                          mlh.LogMethodDetails("GetKnownOperator: FindByActiveDirectoryId returned null for ActiveDirectoryId='{awpUser_ActiveDirectoryId}'", awpUser.ActiveDirectoryId);
        // 				break;
        // 			}
        //
        //                      mlh.LogMethodDetails("GetKnownOperator: FindByActiveDirectoryId returned NOT null for ActiveDirectoryId='{awpUser_ActiveDirectoryId}': Id='{dbUserByAdId_Id}', UserName='{dbUserByAdId_UserName}', ActiveDirectoryId='{dbUserByAdId_ActiveDirectoryId}'", awpUser.ActiveDirectoryId, dbUserByAdId.Id, dbUserByAdId.UserName, dbUserByAdId.ActiveDirectoryId);
        //                      mlh.LogMethodDetails("GetKnownOperator: Searching user in IdentityProvider by login='{dbUserByAdId_UserName}'", dbUserByAdId.UserName);
        // 			Guid adUserIdByLoginFromDatabase;
        // 			try
        // 			{
        // 				adUserIdByLoginFromDatabase = await _identityProvider.GetIdentityIdByLogin(dbUserByAdId.UserName);
        // 			}
        // 			catch (Exception exc)
        // 			{
        //                          mlh.LogMethodWarn(exc, "GetKnownOperator: Searching user in IdentityProvider by login={dbUserByAdId_UserName} FAILED!", dbUserByAdId.UserName);
        // 				break;
        // 			}
        //
        //
        //                      mlh.LogMethodDetails("Searching user in IdentityProvider by login={dbUserByAdId_UserName} resulted not null: ActiveDirectoryId={adUserIdByLoginFromDatabase}", dbUserByAdId.UserName, adUserIdByLoginFromDatabase);
        // 			if (adUserIdByLoginFromDatabase != dbUserByAdId.ActiveDirectoryId)
        // 			{
        //                          mlh.LogMethodDetails("User found in DB by AdId={awpUser_ActiveDirectoryId} with login={dbUserByAdId_UserName} is NOT the same as requested login={identity_Name}, leaving knownOperator null", awpUser.ActiveDirectoryId, dbUserByAdId.UserName, identity.Name);
        // 				break;
        // 			}
        //
        //                      mlh.LogMethodDetails("User found in DB by AdId={awpUser_ActiveDirectoryId} with login={dbUserByAdId_UserName} is the same as requested login={identity_Name}, setting knownOPerator to its value", awpUser.ActiveDirectoryId, dbUserByAdId.UserName, identity.Name);
        // 			knownOperator = dbUserByAdId;
        //
        // 			break;
        // 		case AuthenticationMode.ActiveDirectoryId:
        // 			if (awpUser == null)
        // 			{
        // 				throw new InvalidOperationException("Unable to identify operator for which no user was found in IdentityProvider");
        // 			}
        //
        // 			knownOperator = await operatorDataAccessor.FindByActiveDirectoryId(awpUser.ActiveDirectoryId);
        // 			break;
        // 		default:
        // 			throw new Exception("AuthenticationMode is incorrect");
        // 	}
        //
        //              mlh.LogMethodDetails("Find authenticated operator COMPLETED. Result is {knownOperator}", knownOperator.IfNotNull(op => $"NOT NULL - {op.Id}", "NULL"));
        // }
        // catch (Exception exc)
        // {
        //              mlh.LogMethodError(exc, "Find authenticated operator FAILED!!!");
        // 	throw;
        // }
        //
        // return knownOperator;
    }

    private async Task<AwpUser> TryFindUserInIdentityProvider(IIdentity authenticatedIdentity, OperatorAuthenticationConfiguration configuration)
    {
        using var mlh = this.CreateMethodLogHelper(_logger).WithArgs("authenticatedIdentity={@authenticatedIdentity}", authenticatedIdentity);

        AwpUser awpUser = null;
        try
        {
            var caForLoading = new List<string>
            {
                configuration.ADAttributeNameMapping.FirstNameAttributeName,
                configuration.ADAttributeNameMapping.LastNameAttributeName,
                configuration.ADAttributeNameMapping.MiddleNameAttributeName
            };

            if (configuration.CustomAttributeFillSettings?.Length > 0)
            {
                caForLoading.AddRange(configuration.CustomAttributeFillSettings.Select(p => p.AdAttrName));
            }

            var identityUser = await _identityProvider.GetIdentityByLogin(authenticatedIdentity.Name, caForLoading.ToArray());

            if (identityUser != null)
            {
                awpUser = CreateAwpUser(authenticatedIdentity, identityUser, _identityProvider, configuration);
                mlh.LogMethodDetails("AwpUser found: {awpUser_LastName} {awpUser_FirstName} {awpUser_MiddleName}", awpUser.LastName, awpUser.FirstName, awpUser.MiddleName);
            }
            else
            {
                mlh.LogMethodWarn("AwpUser not found for current Identity: {authenticatedIdentity_Name}", authenticatedIdentity.Name);
            }
        }
        catch (System.DirectoryServices.Protocols.DirectoryOperationException dirOpExc)
        {
            var response = dirOpExc.Response;
            mlh.LogMethodError(dirOpExc, "FindUserInActiveDirectoryOrNull FAILED with DirectoryOperationException! ResultCode='{response_ResultCode}', ErrorMessage='{response_ErrorMessage}', MatchedDN='{response__MatchedDN}'", response?.ResultCode, response?.ErrorMessage, response?.MatchedDN);
        }
        catch (Exception exc)
        {
            //	тут умышленно не делаем throw
            //Этот манагер надо отрефакторить, сейчас логика перенесена из клиента, но она не очень хорошая
            //Например, если настроками указан источник информации НЕ ActiveDirectory - то и искать в каталоге вероятно не нужно.
            //Из этого вытекают остальные переработки
            mlh.LogMethodError(exc, "FindUserInActiveDirectoryOrNull FAILED!");
        }

        return awpUser;
    }

    private AwpUser CreateAwpUser(IIdentity authenticatedIdentity, Identity identityUser, IIdentityProvider provider, OperatorAuthenticationConfiguration configuration)
    {
        var result = new AwpUser
        {
            ActiveDirectoryId = identityUser.Id,
            //	используем это свойство, так как в аудит везде пишется именно оно - чтобы совпадал формат, какой бы он ни был (пофиг, doman\user или user@fulldomain, но одинаково)
            UserName = authenticatedIdentity.Name
        };

        var attributes = identityUser.Attributes?.ToList() ?? new List<Tuple<string, object>>();
        var claimsIdentity = authenticatedIdentity as ClaimsIdentity;

        if (claimsIdentity != null && configuration.ClaimsAttributeNameMapping != null)
        {
            result.FirstName = TryGetClaimValue(claimsIdentity, configuration.ClaimsAttributeNameMapping.FirstNameAttributeName);
            result.LastName = TryGetClaimValue(claimsIdentity, configuration.ClaimsAttributeNameMapping.LastNameAttributeName);
            result.MiddleName = TryGetClaimValue(claimsIdentity, configuration.ClaimsAttributeNameMapping.MiddleNameAttributeName);
        }
        else
        {
            //fill FIO
            result.FirstName = TryGetAttributeValue<string>(provider, attributes, configuration.ADAttributeNameMapping.FirstNameAttributeName);
            result.LastName = TryGetAttributeValue<string>(provider, attributes, configuration.ADAttributeNameMapping.LastNameAttributeName);
            result.MiddleName = TryGetAttributeValue<string>(provider, attributes, configuration.ADAttributeNameMapping.MiddleNameAttributeName);
        }

        //extract CA
        if (configuration.CustomAttributeFillSettings?.Length > 0)
        {
            var ca = new List<Tuple<string, object>>();
            foreach (var customAttribute in configuration.CustomAttributeFillSettings)
            {
                object attributeValue = null;

                if (claimsIdentity != null && !string.IsNullOrWhiteSpace(customAttribute.ClaimType))
                {
                    var claimValue = TryGetClaimValue(claimsIdentity, customAttribute.ClaimType);
                    if (claimValue != null)
                    {
                        switch (customAttribute.CustomAttrType)
                        {
                            case CustomAttributeType.String:
                                attributeValue = claimValue;
                                break;
                            case CustomAttributeType.Bool:
                                attributeValue = bool.Parse(claimValue);
                                break;
                            case CustomAttributeType.Guid:
                                attributeValue = Guid.Parse(claimValue);
                                break;
                            case CustomAttributeType.Long:
                                attributeValue = long.Parse(claimValue);
                                break;
                            case CustomAttributeType.Decimal:
                                attributeValue = decimal.Parse(claimValue);
                                break;
                            case CustomAttributeType.DateTime:
                                attributeValue = DateTime.Parse(claimValue);
                                break;
                            case CustomAttributeType.Binary:
                                throw new NotSupportedException($"Attribute type '{customAttribute.CustomAttrType}' for claims not supported");
                            default:
                                throw new NotSupportedException($"Attribute type '{customAttribute.CustomAttrType}' for claims not supported");
                        }
                    }
                }
                else
                {
                    attributeValue = TryGetAttributeValue(provider, attributes, customAttribute.AdAttrName, customAttribute.GetRealType());
                }

                ca.Add(new Tuple<string, object>(customAttribute.CustomAttrCode, attributeValue));
            }

            result.CustomAttributes = ca;
        }

        return result;
    }

    private static object TryGetAttributeValue(IIdentityProvider provider, List<Tuple<string, object>> resultAttrs, string attributeName, Type resultType)
    {
        object result = null;

        switch (resultType)
        {
            case var type when type == typeof(string):
                result = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                break;
            case var type when type == typeof(bool):
                var stringBool = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (bool.TryParse(stringBool, out var boolValue))
                {
                    result = boolValue;
                }

                break;
            case var type when type == typeof(Guid):
                //	тут не ошибка - этот код работает для атрибутов из AD, там гуид может приходить как строкой, так и байтами
                var byteGuid = TryGetAttributeValue<byte[]>(provider, resultAttrs, attributeName);
                if (byteGuid != null)
                {
                    result = new Guid(byteGuid);
                    break;
                }

                var stringGuid = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (Guid.TryParse(stringGuid, out var guidValue))
                {
                    result = guidValue;
                }

                break;
            case var type when type == typeof(int):
                var stringInt = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (int.TryParse(stringInt, out var intValue))
                {
                    result = intValue;
                }

                break;
            case var type when type == typeof(long):
                var stringLong = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (long.TryParse(stringLong, out var longValue))
                {
                    result = longValue;
                }

                break;
            case var type when type == typeof(int?):
                var stringIntNullable = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (int.TryParse(stringIntNullable, out var intNullableValue))
                {
                    result = intNullableValue;
                }

                break;
            case var type when type == typeof(long?):
                var stringLongNullable = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (long.TryParse(stringLongNullable, out var longNullableValue))
                {
                    result = longNullableValue;
                }

                break;
            case var type when type == typeof(DateTime):
                var stringDate = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (DateTime.TryParse(stringDate, out var dateValue))
                {
                    result = dateValue;
                }

                break;
            case var type when type == typeof(decimal):
                var stringDecimal = TryGetAttributeValue<string>(provider, resultAttrs, attributeName);
                if (decimal.TryParse(stringDecimal, out var decimalValue))
                {
                    result = decimalValue;
                }

                break;
            case var type when type == typeof(byte[]):
                result = TryGetAttributeValue<byte[]>(provider, resultAttrs, attributeName);
                break;
        }

        return result;
    }

    private static T TryGetAttributeValue<T>(IIdentityProvider provider, List<Tuple<string, object>> attributesCollection, string attributeName)
    {
        var value = attributesCollection.FirstOrDefault(p => p.Item1 == attributeName);
        var result = value != null ? provider.ConvertAttribute<T>(value.Item2) : default;
        return result;
    }

    private Operator BuildOperator(Operator authenticatedOperatorData, IIdentity identityData, AwpUser activeDirectoryUserData, DataSourceType priorityDataSource)
    {
        if (authenticatedOperatorData == null)
        {
            throw new ArgumentNullException(nameof(authenticatedOperatorData));
        }

        var simpleOperator = new Operator
        {
            Id = authenticatedOperatorData.Id,
            UserName = authenticatedOperatorData.UserName,
            Nickname = authenticatedOperatorData.Nickname,
            ActiveDirectoryId = authenticatedOperatorData.ActiveDirectoryId,
            CustomAttributes = authenticatedOperatorData.CustomAttributes
        };

        switch (priorityDataSource)
        {
            case DataSourceType.Database:
                simpleOperator.FirstName = authenticatedOperatorData.FirstName;
                simpleOperator.MiddleName = authenticatedOperatorData.MiddleName;
                simpleOperator.LastName = authenticatedOperatorData.LastName;
                break;
            case DataSourceType.ActiveDirectory:
                simpleOperator.FirstName = activeDirectoryUserData?.FirstName;
                simpleOperator.MiddleName = activeDirectoryUserData?.MiddleName;
                simpleOperator.LastName = activeDirectoryUserData?.LastName;
                break;
            case DataSourceType.Identity:
                simpleOperator.FirstName = identityData?.Name;
                simpleOperator.MiddleName = string.Empty;
                simpleOperator.LastName = string.Empty;
                break;

            default:
                throw new ArgumentOutOfRangeException(nameof(priorityDataSource), priorityDataSource, null);
        }

        return simpleOperator;
    }
}
