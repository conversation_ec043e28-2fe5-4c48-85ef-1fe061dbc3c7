using System.Runtime.CompilerServices;
using Platform.Common;
using Platform.Logging.MicrosoftExtensions;
using Product.AWP.Infrastructure.Controllers.Base;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.Helpers;

public class AuditExecutionHelper
{
    private static IAuditor _auditor;
    private readonly InfrastructureApiControllerBase _controller;
    private readonly string _operationSuffixForAudit;

    protected AuditExecutionHelper(ILoggerFactory loggerFactory, IAuditor auditor, string operationSuffixForAudit = null)
    {
        LoggerFactory = loggerFactory;
        _operationSuffixForAudit = operationSuffixForAudit;
        _auditor = auditor;
    }

    protected AuditExecutionHelper(InfrastructureApiControllerBase controller, IAuditor auditor, ILoggerFactory loggerFactory, string operationSuffixForAudit = null)
        : this(loggerFactory, auditor, operationSuffixForAudit)
    {
        _controller = controller;
        _auditor = auditor;
    }

    protected ILoggerFactory LoggerFactory { get; }

    public static AuditExecutionHelper CreateForController(InfrastructureApiControllerBase controller, IAuditor auditor, ILoggerFactory loggerFactory, string operationSuffixForAudit = null)
    {
        return new AuditExecutionHelper(controller, auditor, loggerFactory, operationSuffixForAudit);
    }

    public async Task<T> ExecuteWithTryWithAudit<T, TDataAccessor>(IMethodLogHelper mlh, Func<TDataAccessor> accessorCreator, Func<TDataAccessor, Task<T>> func, string methodPartName = null, [CallerMemberName] string operationName = null)
        where TDataAccessor : class
    {
        return await mlh.ExecuteWithTryAsync(async () => // TODO: Тут может быть проблема т.к. перешли на репозитории с интерфейсами
        {
            var dataAccessor = accessorCreator();

            if (dataAccessor is not IAuditable daAsAuditable)
            {
                mlh.LogMethodDetails("dataAccessor is not IAuditable...");
                return await mlh.ExecuteWithTryAsync(async () => await func(dataAccessor), methodPartName);
            }

            mlh.LogMethodDetails("dataAccessor is IAuditable...");
            IAuditor auditor = null;
            try
            {
                auditor = InitAuditor(operationName, daAsAuditable);
            }
            catch (Exception exc)
            {
                mlh.LogMethodFailed(exc, "InitAuditor FAILED!");
                throw;
            }

            try
            {
                return await mlh.ExecuteWithTryAsync(
                    async () => { return await func(dataAccessor); },
                    methodPartName
                );
            }
            finally
            {
                auditor?.DisposeIfDisposable();
            }
        });
    }

    private IAuditor InitAuditor(string operationName, IAuditable daAsAuditable)
    {
        if (_controller != null)
        {
            return InitAuditorForController(_controller, operationName, daAsAuditable);
        }

        return null;
    }

    private IAuditor InitAuditorForController(InfrastructureApiControllerBase controller, string operationName, IAuditable daAsAuditable)
    {
        var auditor = _auditor;
        daAsAuditable.SetAuditor(auditor);
        var operationNameForAudit = string.IsNullOrEmpty(_operationSuffixForAudit)
            ? operationName
            : $"{operationName} ({_operationSuffixForAudit})";
        daAsAuditable.InitAuditOnAuditable(auditor, controller.HttpContext, operationNameForAudit);
        return auditor;
    }

    public async Task ExecuteWithTryWithAudit<TDataAccessor>(IMethodLogHelper mlh, Func<TDataAccessor> accessorCreator, Func<TDataAccessor, Task> func, string operationPartName = null, [CallerMemberName] string operationName = null)
        where TDataAccessor : class
    {
        await ExecuteWithTryWithAudit(
            mlh,
            accessorCreator,
            async dataAccessor =>
            {
                await func(dataAccessor);
                return 1;
            },
            operationPartName, operationName);
    }
}
