using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Product.AWP.Infrastructure.DAL.Entities.Audit;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL;

public abstract class AuditDbContextBase : DbContext, IAuditDbContext
{
    protected const string DefaultConnectionStringName = "AWP.Audit";
    protected const string DefaultDefaultSchema = "AWP_INFRA_AUD";

    static AuditDbContextBase()
    {
        var builder = new ConfigurationBuilder().AddJsonFile("appsettings.json", true);
        ConfigurationRoot = builder.Build();
    }

    protected static IConfigurationRoot ConfigurationRoot { get; }
    protected IConfiguration Configuration { get; }

    public string ConnectionStringName { get; protected set; }
    public string DefaultSchema { get; protected set; }

    public async Task<int> SaveChangesAsync()
    {
        var date = DateTime.UtcNow;
        foreach (var addedAudit in ChangeTracker.Entries<Audit>().Where(aud => aud.State == EntityState.Added))
        {
            addedAudit.Entity.ChangeDate = date;
        }

        return await base.SaveChangesAsync();
    }

    protected string GetConnectionString(string connectionStringName)
    {
        return Configuration != null
            ? Configuration.GetConnectionString(connectionStringName)
            : ConfigurationRoot.GetConnectionString(connectionStringName);
    }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema(DefaultSchema);

        modelBuilder.ApplyConfiguration(new Configurations.Audit.Audit());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ActualOperatorStatus());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.AdministrationObject());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.AdministrationPermission());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.AesQueue());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.AesQueueUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Application());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ApplicationUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.WebApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ExternalApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ControlApplicationInitialization());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.BaseShortcut());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Configuration());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.CrossWorkflow());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.CustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Layout());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.LayoutUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.LoginWorkflow());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Module());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ModuleUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Operator());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorCustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorGroupCustomAttribute());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorOperatorGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorStatus());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.OperatorStatusUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Permission());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.PermissionUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ServiceArea());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ServiceAreaConfiguration());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ServiceAreaType());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Shortcut());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.ShortcutUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.StatusTransition());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserShortcut());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Workflow());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.WorkflowUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.WorkflowGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Workplace());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserGroup());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.Identity());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserGroupIdentity());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserGroupWorkplace());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserGroupServiceArea());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserGroupUserRole());
        modelBuilder.ApplyConfiguration(new Configurations.Audit.UserRole());
    }

    #region ctors

    protected AuditDbContextBase()
        : this(DefaultConnectionStringName, DefaultDefaultSchema)
    {
    }

    protected AuditDbContextBase(string defaultSchema)
        : this(DefaultConnectionStringName, defaultSchema)
    {
    }

    protected AuditDbContextBase(IConfiguration configuration, string defaultSchema)
        : this(configuration, DefaultConnectionStringName, defaultSchema)
    {
    }

    protected AuditDbContextBase(string connectionStringName, string defaultSchema)
    {
        ConnectionStringName = connectionStringName;
        DefaultSchema = defaultSchema;
    }

    protected AuditDbContextBase(IConfiguration configuration, string connectionStringName, string defaultSchema)
    {
        Configuration = configuration;
        ConnectionStringName = connectionStringName;
        DefaultSchema = defaultSchema;
    }

    #endregion

    #region DbSets

    public DbSet<Audit> Audits { get; set; }
    public DbSet<UserGroupWorkplace> UserGroupWorkplaces { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<PermissionUserRole> PermissionUserRoles { get; set; }
    public DbSet<ServiceArea> ServiceAreas { get; set; }
    public DbSet<UserGroup> UserGroups { get; set; }
    public DbSet<Identity> Identities { get; set; }
    public DbSet<UserGroupIdentity> UserGroupIdentities { get; set; }
    public DbSet<UserGroupServiceArea> UserGroupServiceAreas { get; set; }
    public DbSet<UserGroupUserRole> UserGroupUserRoles { get; set; }
    public DbSet<ActualOperatorStatus> ActualOperatorStatuses { get; set; }
    public DbSet<AdministrationObject> AdministrationObjects { get; set; }
    public DbSet<AdministrationPermission> AdministrationPermissions { get; set; }
    public DbSet<AesQueue> AesQueues { get; set; }
    public DbSet<AesQueueUserRole> AesQueueUserRoles { get; set; }
    public DbSet<Application> Applications { get; set; }
    public DbSet<ApplicationUserRole> ApplicationUserRoles { get; set; }
    public DbSet<ApplicationInitialization> ApplicationInitializations { get; set; }
    public DbSet<ControlApplicationInitialization> ControlApplicationInitializations { get; set; }
    public DbSet<ExternalApplicationInitialization> ExternalApplicationInitializations { get; set; }
    public DbSet<WebApplicationInitialization> WebApplicationInitializations { get; set; }
    public DbSet<BaseShortcut> BaseShortcuts { get; set; }
    public DbSet<Configuration> Configurations { get; set; }
    public DbSet<CrossWorkflow> CrossWorkflows { get; set; }
    public DbSet<CustomAttribute> CustomAttributes { get; set; }
    public DbSet<Layout> Layouts { get; set; }
    public DbSet<LayoutUserRole> LayoutUserRoles { get; set; }
    public DbSet<LoginWorkflow> LoginWorkflows { get; set; }
    public DbSet<Module> Modules { get; set; }
    public DbSet<ModuleUserRole> ModuleUserRoles { get; set; }
    public DbSet<Operator> Operators { get; set; }
    public DbSet<OperatorCustomAttribute> OperatorCustomAttributes { get; set; }
    public DbSet<OperatorGroup> OperatorGroups { get; set; }
    public DbSet<OperatorGroupCustomAttribute> OperatorGroupCustomAttributes { get; set; }
    public DbSet<OperatorOperatorGroup> OperatorOperatorGroups { get; set; }
    public DbSet<OperatorStatus> OperatorStatuses { get; set; }
    public DbSet<OperatorStatusUserRole> OperatorStatuseUserRoles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<ServiceAreaConfiguration> ServiceAreaConfigurations { get; set; }
    public DbSet<ServiceAreaType> ServiceAreaTypes { get; set; }
    public DbSet<Shortcut> Shortcuts { get; set; }
    public DbSet<ShortcutUserRole> ShortcutUserRoles { get; set; }
    public DbSet<StatusTransition> StatusTransitions { get; set; }
    public DbSet<UserShortcut> UserShortcuts { get; set; }
    public DbSet<Workflow> Workflows { get; set; }
    public DbSet<WorkflowUserRole> WorkflowUserRoles { get; set; }
    public DbSet<WorkflowGroup> WorkflowGroups { get; set; }
    public DbSet<Workplace> Workplaces { get; set; }

    #endregion

    //public DbSet GetDbSet(object entity)
    //{
    //	return Set(entity.GetType());
    //}
}
