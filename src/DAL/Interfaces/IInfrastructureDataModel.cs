using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Product.AWP.Infrastructure.DAL.Entities;
using Product.AWP.Infrastructure.DAL.Interfaces.Audit;

namespace Product.AWP.Infrastructure.DAL.Interfaces;

public interface IInfrastructureDataModel : IAuditable, IDisposable
{
    DbSet<AdministrationObject> AdministrationObjects { get; set; }
    DbSet<AdministrationPermission> AdministrationPermissions { get; set; }

    DbSet<UserGroup> UserGroups { get; set; }
    DbSet<Identity> Identities { get; set; }
    DbSet<UserRole> UserRoles { get; set; }

    DbSet<CustomAttribute> CustomAttributes { get; set; }

    DbSet<Configuration> Configurations { get; set; }

    DbSet<ConfigurationVersion> ConfigurationVersions { get; set; }

    DbSet<ServiceAreaType> ServiceAreaTypes { get; set; }

    DbSet<ServiceArea> ServiceAreas { get; set; }

    DbSet<ServiceAreaConfiguration> ServiceAreaConfigurations { get; set; }

    DbSet<Operator> Operators { get; set; }

    DbSet<OperatorCustomAttribute> OperatorsCustomAttributes { get; set; }

    DbSet<OperatorGroup> OperatorGroups { get; set; }

    DbSet<OperatorGroupCustomAttribute> OperatorGroupCustomAttributes { get; set; }

    DbSet<OperatorGroupOperatorLink> OperatorGroupOperatorLinks { get; set; }


    DbSet<OperatorStatus> OperatorStatuses { get; set; }

    DbSet<StatusTransition> StatusTransitions { get; set; }

    DbSet<ActualOperatorStatus> ActualOperatorStatuses { get; set; }

    DbSet<ServiceAreaAudit> ServiceAreaAudits { get; set; }

    DbSet<Module> Modules { get; set; }

    DbSet<Application> Applications { get; set; }

    DbSet<ApplicationInitialization> ApplicationInitializations { get; set; }
    DbSet<ControlApplicationInitialization> ControlApplicationInitialization { get; set; }
    DbSet<ExternalApplicationInitialization> ExternalApplicationInitialization { get; set; }
    DbSet<WebApplicationInitialization> WebApplicationInitialization { get; set; }

    DbSet<LoginWorkflow> LoginWorkflows { get; set; }

    DbSet<Layout> Layouts { get; set; }

    DbSet<Permission> Permissions { get; set; }

    DbSet<WorkflowGroup> WorkflowGroups { get; set; }
    DbSet<Workflow> Workflows { get; set; }
    DbSet<CrossWorkflow> CrossWorkflows { get; set; }

    DbSet<BaseShortcut> BaseShortcuts { get; set; }
    DbSet<Shortcut> Shortcuts { get; set; }
    DbSet<UserShortcut> UserShortcuts { get; set; }

    DbSet<AesQueue> AesQueues { get; set; }

    DbSet<WorkSession> WorkSessions { get; set; }
    DbSet<Workplace> Workplaces { get; set; }
    DbSet<WebAppProxySetting> WebAppProxySettings { get; set; }

    DatabaseFacade Database { get; }

    DbSet<TEntity> GetDbSet<TEntity>() where TEntity : class;

    TEntity Add<TEntity>(TEntity entityToAdd) where TEntity : class;
    Task<TEntity> AddAsync<TEntity>(TEntity entityToAdd) where TEntity : class;

    TEntity Delete<TEntity>(TEntity entityToDelete) where TEntity : class;

    EntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;

    Task<int> SaveChangesAsync();
}
