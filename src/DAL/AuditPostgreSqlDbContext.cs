using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Product.AWP.Infrastructure.DAL;

public class AuditPostgreSqlDbContext : AuditDbContextBase
{
    public AuditPostgreSqlDbContext()
        : this("Z")
    {
    }

    public AuditPostgreSqlDbContext(string defaultSchema)
        : base(defaultSchema)
    {
    }

    public AuditPostgreSqlDbContext(IConfiguration configuration, string defaultSchema)
        : base(configuration, defaultSchema)
    {
    }

    public AuditPostgreSqlDbContext(string connectionString, string defaultSchema)
        : base(connectionString, defaultSchema)
    {
    }

    public AuditPostgreSqlDbContext(IConfiguration configuration, string connectionString, string defaultSchema)
        : base(configuration, connectionString, defaultSchema)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        TuneProperties(modelBuilder);
    }

    private void TuneProperties(ModelBuilder modelBuilder)
    {
        //	была мысль все строковые пропертя в БД хранить в типе text, но не стал - пусть со стороны БД будет ограничение по длине там, где задано в модели тоже. Чтобы одинаково было. По производительности нет разницы, согласно документации Postgre
        //	https://www.postgresql.org/docs/current/datatype-character.html
        //	https://stackoverflow.com/questions/4848964/postgresql-difference-between-text-and-varchar-character-varying
        //modelBuilder.Properties<string>().Configure(x => x.HasColumnType("text"));

        foreach (var e in modelBuilder.Model.GetEntityTypes())
        {
            if (e == null) continue;

            foreach (var mutableProperty in e.GetProperties())
            {
                if (mutableProperty == null || mutableProperty.PropertyInfo == null) continue;
                if (mutableProperty.PropertyInfo.PropertyType == typeof(DateTimeOffset) || mutableProperty.PropertyInfo.PropertyType == typeof(DateTimeOffset?))
                {
                    //	в постгре для типа "timestamp with time zone" максимальный пресижен 6
                    mutableProperty.SetPrecision(6);
                }
            }
        }
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        if (!optionsBuilder.IsConfigured)
        {
            var connection = GetConnectionString(DefaultConnectionStringName);
            optionsBuilder.UseNpgsql(connection);
        }
    }
}
