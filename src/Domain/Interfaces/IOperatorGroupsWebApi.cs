using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

public interface IOperatorGroupsWebApi
{
    /// <summary>
    /// Получить все группы операторов
    /// </summary>
    /// <returns></returns>
    Task<OperatorGroup[]> GetAllGroups();

    /// <summary>
    /// Добавляет оператора в группу
    /// </summary>
    Task AddOperatorToGroup(Guid operatorId, Guid operatorGroupId);

    /// <summary>
    /// Удаляет оператора из группы
    /// </summary>
    Task RemoveOperatorFromGroup(Guid operatorId, Guid operatorGroupId);

    /// <summary>
    /// Возвращает True если оператор принадлежит к группе
    /// </summary>
    /// <returns></returns>
    Task<bool> IsOperatorInGroup(Guid operatorId, Guid operatorGroupId, bool searchInChilds);

    /// <summary>
    /// Получить кастомные атрибуты для указаного списка групп операторов	
    /// </summary>
    /// <param name="operatorGroupIds">Список идентификаторов групп операторов для которых требуется получить кастомные атрибуты</param>
    /// <param name="codes">Список кодов кастомных атрибутов которые нужно получить. Если null - получить все какие есть</param>
    /// <returns>Список кастомных атрибутов</returns>
    Task<OperatorGroupCustomAttribute[]> GetCustomAttributesForOperatorGroups(Guid[] operatorGroupIds, string[] codes);

    /// <summary>
    /// Добавить или обновить кастомные атрибуты группы операторов
    /// </summary>
    /// <param name="attributes">Список атрибутов</param>
    Task AddOrUpdateCustomAttributes(OperatorGroupCustomAttribute[] attributes);

    /// <summary>
    /// Удалить кастомные атрибуты группы операторов
    /// </summary>
    /// <param name="operatorGroupId">Идентификатор группы операторов</param>
    /// <param name="codes">Список кодов кастомных атрибутов которые нужно удалить</param>
    Task RemoveCustomAttribute(Guid operatorGroupId, string[] codes);

    /// <summary>
    /// Получить кастомные атрибуты для указаной группы операторов	
    /// </summary>
    /// <param name="operatorGroupId">Идентификатор группы операторов</param>
    /// <param name="codes">Список кодов кастомных атрибутов которые нужно получить. Если null - получить все какие есть</param>
    /// <returns>Список кастомных атрибутов</returns>
    Task<OperatorGroupCustomAttribute[]> GetCustomAttributes(Guid operatorGroupId, string[] codes);

    /// <summary>
    /// Возвращает группы оператора с учётом иерархии групп.
    /// </summary>
    /// <param name="operatorId">Идентификатор оператора.</param>
    /// <returns>Коллекция идентификаторов групп оператора.</returns>
    Task<Guid[]> GetOperatorGroupsIdsWithParentGroupsInHierarchy(Guid operatorId);
}
