using Product.AWP.Infrastructure.Domain.Entities;

namespace Product.AWP.Infrastructure.Domain.Interfaces;

public interface IShortcutsWebApi
{
    Task<Shortcut[]> GetShortcutsForCurrentUser(Guid userRoleId, Guid? serviceAreaId);
    Task<UserShortcut[]> GetCurrentUserCustomShortcuts();
    Task AddCurrentUserCustomShortcut(UserShortcut shortcut);
    Task UpdateCurrentUserCustomShortcut(UserShortcut shortcut);
    Task RemoveCurrentUserCustomShortcut(Guid userShortcutId);
}
